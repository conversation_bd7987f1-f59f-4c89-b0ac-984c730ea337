name: Docker Image CI

on:
  push:
    branches: [ "main" ]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Login to Aliyun Container Registry
      uses: docker/login-action@v3
      with:
        registry: registry.cn-shenzhen.aliyuncs.com
        username: imbrook
        password: ${{ secrets.ALI_DOCKER_TOKEN }}
    - name: Build the Docker image
      run: |
        tag_path=registry.cn-shenzhen.aliyuncs.com/jeelyton/fast-flow:$(date +"%y%m.%H%M")
        docker build . --file Dockerfile --tag $tag_path
        docker push $tag_path
