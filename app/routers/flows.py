from typing import List

import httpx
from fastapi import APIRouter, Depends
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from starlette.responses import FileResponse, Response

from app.database.redis_client import redis_cache, CachePrefixes
from app.flows.delivery_notice.delivery_notice_strategy import DeliveryNoticeFactory
from app.flows.pdf2excel import pdf2excel
from app.routers.kingd import form_view, KDViewModel
from app.schemas.common import S3File
from app.utils.app_config import app_config
from app.utils.auth_util import get_current_user, get_auth_credentials, UserInfo
from app.utils.kingd_util import KDFormIds

router = APIRouter()

class CustomerPOInput(BaseModel):
    input_files: List[S3File] = Field(min_length=1)

@router.post("/extract-customer-po")
async def extract_customer_po(params: CustomerPOInput, current_user: UserInfo = Depends(get_current_user)):
    excel_path, file_name = await pdf2excel(params.input_files[0])
    return FileResponse(excel_path, filename=file_name)

class DeliveryNoticeModel(BaseModel):
    outstock_no: str
class DeliveryNoticesModel(BaseModel):
    outstock_nos: list[str]


@redis_cache(prefix=CachePrefixes.OutstockDoc, enabled= app_config.is_dev, ex = 86400)
async def get_outstock_info(outstock_no: str):
    outstock_json = await form_view(
        KDViewModel(
            Number = outstock_no,
        ),
        KDFormIds.DeliveryNotice
    )
    return outstock_json
@router.post('/delivery_notice')
async def delivery_notice(model: DeliveryNoticeModel, current_user: UserInfo = Depends(get_current_user), credentials: HTTPAuthorizationCredentials = Depends(get_auth_credentials)):
    outstock_json = await get_outstock_info(model.outstock_no)

    strategy = DeliveryNoticeFactory.get_strategy(outstock_json, current_user)
    res = await strategy.run()
    if isinstance(res, httpx.Response):
        return Response(content=res.content, status_code=res.status_code, headers=res.headers)
    return res

@router.post('/delivery_notices')
async def delivery_notices(model: DeliveryNoticesModel, current_user: UserInfo = Depends(get_current_user)):
    pass
    # outstock_info = await get_outstock_info(model.outstock_no)
    #
    # strategy = DeliveryNoticeFactory.get_strategy(outstock_info, current_user)
    # res = await strategy.run()
    # if isinstance(res, httpx.Response):
    #     return Response(content=res.content, status_code=res.status_code, headers=res.headers)
    # return res

