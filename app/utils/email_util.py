import logging
import smtplib
from email import encoders
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path

from app.routers.users import Credential


logger = logging.getLogger(__name__)

__smpt_servers = {
    'jeelyton.cn': 'smtp.exmail.qq.com',
    'jeelyton.com':'smtp.qiye.163.com',
}

def get_smtp_server(sender: str):
    domain = sender.split('@')[-1]
    smtp_server = __smpt_servers.get(domain)
    if not smtp_server:
        raise Exception(f'SMTP server {domain} not found.')
    return smtp_server

def _normalize_address(address: str | list[str]) -> list[str]:
    return [address] if isinstance(address, str) else address


def send_email(credential: Credential, to_addrs: list[str] | str, subject: str, body: str = "", cc_addrs: list[str] | str = None, attachments: list[Path] = None):
    smtp_server = get_smtp_server(credential.login_id)
    message = MIMEMultipart()
    send_from = credential.login_id
    if credential.name:
        send_from = f"{credential.name} <{send_from}>"
    message["From"] = send_from
    message["Subject"] = subject
    to_addrs = _normalize_address(to_addrs)
    cc_addrs = _normalize_address(cc_addrs or [])
    message["To"] = ','.join(to_addrs)
    if cc_addrs:
        message["Cc"] = ','.join(cc_addrs)
    all_recipients = to_addrs + cc_addrs
    message.attach(MIMEText(body, "html", 'utf-8'))

    if attachments:
        for attachment_path in attachments:
            attachment = MIMEBase("application", "octet-stream")
            attachment.set_payload(attachment_path.read_bytes())
            encoders.encode_base64(attachment)
            attachment.add_header("Content-Disposition", f"attachment; filename={attachment_path.name}")
            message.attach(attachment)
            message.attach(attachment)


    with smtplib.SMTP(smtp_server, 587) as server:
        server.starttls()
        server.login(credential.login_id, credential.password)
        server.sendmail(credential.login_id, all_recipients, message.as_string())
    logger.info(f"Email sent {to_addrs}: {subject}")