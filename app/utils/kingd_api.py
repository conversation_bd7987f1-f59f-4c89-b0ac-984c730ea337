import hashlib
import logging
import time
from typing import List, Callable, Awaitable

from httpx import Request, Response, AsyncBaseTransport, AsyncHTTPTransport, AsyncClient
import jmespath

from app.database.redis_client import set_credential, CredentialType, get_credential, del_credential
from app.utils.app_config import app_config

logger = logging.getLogger(__name__)

__kd_api_url = app_config.kd_api_url
__kd_account_id = app_config.kd_account_id
__kd_app_id = app_config.kd_app_id
__kd_app_secret = app_config.kd_app_secret


def get_sha256(arr: List[str]) -> str:

    # Sort array using ordinal (binary) comparison
    arr_sorted = sorted(arr, key=lambda s: s.encode('utf-8'))

    # Join strings without separator
    combined = ''.join(arr_sorted)

    # Compute SHA256 hash
    data = combined.encode('utf-8')
    hash_bytes = hashlib.sha256(data).digest()

    # Convert to hexadecimal string (uppercase)
    return hash_bytes.hex()

# https://vip.kingdee.com/knowledge/specialDetail/650386937144032256?category=650388108193709056&id=650369502764414208&type=Knowledge&productLineId=1&lang=zh-CN
def get_params_with_sign(uid: str):
    timestamp = str(int(time.time()))

    sign_params = [__kd_account_id, uid, __kd_app_id, __kd_app_secret, timestamp]

    sign = get_sha256(sign_params)
    return [ __kd_account_id, uid, __kd_app_id, timestamp, sign, 2052]

async def fetch_credential(uid: str):
    res_json = await login_by_sign(uid)
    session_id = res_json.get('KDSVCSessionId')
    assert session_id, res_json.get('Message', '登录出错！')
    return session_id


async def attach_credential(request: Request):
    print('hooks')
    uid = request.extensions.get('uid')
    assert uid, 'httpx extensions.uid is required'
    session_id = await get_credential(CredentialType.KingDee, uid)
    if not session_id:
        session_id = await fetch_credential(uid)
        await set_credential(CredentialType.KingDee, uid, session_id)
        logger.warning(f'kdsession refreshed for {uid}')
    request.headers['Cookie'] = f'kdservice-sessionid={session_id}'
    request.extensions['start_time'] = time.time()


class KDRetryTransport(RetryLoginTransport):
    async def retry_by_response(self, response: Response):
        await response.aread()
        res_json = response.json()
        error_message = jmespath.search('Result.ResponseStatus.Errors[0].Message', res_json)
        if error_message == '会话信息已丢失，请重新登录':
            return True
        return False
    async def before_retry(self, request: Request):
        await del_credential(CredentialType.KingDee, request.extensions.get('uid'))

kd_api = AsyncClient(
    base_url=__kd_api_url,
    transport=KDRetryTransport(
        request_hooks=[attach_credential]
    ),
    event_hooks={'request': [attach_credential]},
    timeout=60
)

async def login_by_sign(uid: str):
    async with AsyncClient(base_url=__kd_api_url) as client:
        parameters = get_params_with_sign(uid)
        res = await client.post('/Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginBySign.common.kdsvc', json = { "parameters": parameters })
        res.raise_for_status()
        return res.json()
async def login_by_password(name: str, password: str):
    async with AsyncClient(base_url=__kd_api_url) as client:
        res = await client.post('/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc', json = {"parameters":[__kd_account_id, name, password,2052]})
        res.raise_for_status()
        return res.json()
