import enum
import functools
import json
import os

from redis.asyncio import Redis
import logging

from app.utils.app_config import app_config

logger = logging.getLogger(__name__)
# logger.setLevel(logging.DEBUG)

redis_client = Redis.from_url(
    str(app_config.redis_url),
    decode_responses=True,
    socket_connect_timeout=5,  # Timeout for initial connection
    socket_timeout=5,          # Timeout for individual commands
    retry_on_timeout = True,
)


class CredentialType(enum.StrEnum):
    General = 'GN'
    KingDee = 'KD'
    XieKeYun = 'XKY'
    ChengCheng004 = 'CC4'
    QiYi = 'QY'


class CachePrefixes(enum.StrEnum):
    Credential = 'cred'
    OutstockDoc = 'STOA'

async def get_credential(ctype: CredentialType, uid: str):
    cred = await get_str(f'{CachePrefixes.Credential}:{ctype}:{uid}')
    logger.debug(f'get credential for {ctype}:{uid} is {cred}')
    return cred

async def set_credential(ctype: CredentialType, uid: str, credential: any, **kwargs):
    logger.debug(f'set credential for {ctype}:{uid} to {credential}')
    await set_str(f'{CachePrefixes.Credential}:{ctype}:{uid}', credential, **kwargs)

async def del_credential(ctype: CredentialType, uid: str):
    logger.debug(f'del credential for {ctype}:{uid}')
    await redis_client.delete(f'{CachePrefixes.Credential}:{ctype}:{uid}')


async def get_str(key: str):
    val = await redis_client.get(key)
    if val and val.startswith('{'):
        val = json.loads(val)
    return val

async def set_str(key: str, val: any, **kwargs):
    val = json.dumps(val) if not isinstance(val, str) else val
    return await redis_client.set(key, val, **kwargs)


def redis_cache(prefix: str, enabled = True, **cache_kwargs):
    """异步 Redis 缓存装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            if not enabled:
                return await func(*args, **kwargs)
            # 生成缓存键
            key_parts = [prefix]
            key_parts.extend([str(arg) for arg in args])
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}={v}")
            cache_key = ":".join(key_parts)

            try:
                # 异步获取缓存
                cached_data = await get_str(cache_key)
                if cached_data is not None:
                    return cached_data
            except Exception as e:
                logger.error(f"redis_cache read error: {e}")

            # 执行原函数
            result = await func(*args, **kwargs)

            try:
                # 异步写入缓存
                await set_str(cache_key, result, **cache_kwargs)
            except Exception as e:
                logger.error(f"redis_cache write error: {e}")

            return result

        return wrapper

    return decorator
