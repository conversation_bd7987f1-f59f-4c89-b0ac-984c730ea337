import asyncio
from typing import Callable, Awaitable, List

import httpx
import jmespath
from httpx import Request, Response, AsyncBaseTransport, AsyncHTTPTransport

from app.database.redis_client import del_credential, CredentialType
from app.utils.kingd_api import attach_credential, __kd_api_url





async def run():
    res = await kd_api.post('/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc', json={
        "data": {
            "CreateOrgId": 0,
            "Number": "SZ-STOA25074027",
            "Id": ""
        },
        "formid": "SAL_DELIVERYNOTICE"
    }, extensions={'uid': '周雪玲'})
    print(11, res.json())

if __name__ == '__main__':
    asyncio.run(run())